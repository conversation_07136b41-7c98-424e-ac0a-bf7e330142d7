﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Grid ColumnDefinitions="*,*" Background="White">
            <StackPanel Grid.Column="0" Margin="24" Spacing="12">
                <RadioButton>
                    <Button Content="Check or Not" />
                </RadioButton>
                <RadioButton IsEnabled="False">
                    <TextBlock Text="Not" />
                </RadioButton>
                <RadioButton IsChecked="True">
                    <TextBlock Text="Checked" />
                </RadioButton>
                <RadioButton IsThreeState="True" IsChecked="{x:Null}">
                    <TextBlock Text="Or Maybe?" />
                </RadioButton>
            </StackPanel>
            <StackPanel Grid.Column="1" Margin="24" Spacing="12">
                <RadioButton Theme="{StaticResource AlternativeRadioButtonTheme}">
                    <Button Content="Check or Not" />
                </RadioButton>
                <RadioButton Theme="{StaticResource AlternativeRadioButtonTheme}" IsEnabled="False">
                    <TextBlock Text="Not" />
                </RadioButton>
                <RadioButton Theme="{StaticResource AlternativeRadioButtonTheme}" IsChecked="True">
                    <TextBlock Text="Checked" />
                </RadioButton>
                <RadioButton Theme="{StaticResource AlternativeRadioButtonTheme}" IsThreeState="True"
                             IsChecked="{x:Null}">
                    <TextBlock Text="Or Maybe?" />
                </RadioButton>
            </StackPanel>
        </Grid>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type RadioButton}" TargetType="RadioButton">
        <Setter Property="Background" Value="{StaticResource ControlInteractiveBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="6" Background="{StaticResource TransparentBrush}">
                    <Border Grid.Column="0" Name="Box" Cursor="Hand"
                            MinHeight="{TemplateBinding FontSize}"
                            MinWidth="{TemplateBinding FontSize}"
                            Height="16"
                            Width="16"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                        <Panel>
                            <Border Name="Indicator" Margin="2" CornerRadius="{StaticResource FullCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}"
                                    RenderTransform="scale(0.0)">
                                <Border.Transitions>
                                    <Transitions>
                                        <DoubleTransition Property="Opacity" Easing="CubicEaseOut"
                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                        <TransformOperationsTransition Property="RenderTransform"
                                                                       Easing="CubicEaseOut"
                                                                       Duration="{StaticResource ControlInstantAnimationDuration}" />
                                    </Transitions>
                                </Border.Transitions>
                            </Border>
                        </Panel>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter"
                                      Grid.Column="1"
                                      Margin="{TemplateBinding Padding}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      IsVisible="{TemplateBinding Content,
                                                        Converter={x:Static ObjectConverters.IsNotNull}}"
                                      RecognizesAccessKey="True"
                                      TextElement.Foreground="{TemplateBinding Foreground}"
                                      TextElement.FontSize="{TemplateBinding FontSize}"
                                      TextElement.FontWeight="{TemplateBinding FontWeight}" />
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:checked">
            <Style Selector="^ /template/ Border#Box">
                <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(1.0)" />
            </Style>

            <Style Selector="^:pointerover /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(1.2)" />
            </Style>

            <Style Selector="^:pressed /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(0.8)" />
            </Style>
        </Style>

        <Style Selector="^:unchecked">
            <Style Selector="^:pointerover">
                <Style Selector="^ /template/ Border#Box">
                    <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
                </Style>
            </Style>
        </Style>

        <Style Selector="^:disabled /template/ Border#Box">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="AlternativeRadioButtonTheme" TargetType="RadioButton">
        <Setter Property="Background" Value="{StaticResource ControlInteractiveBackgroundBrush}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="6" Background="{StaticResource TransparentBrush}">
                    <Border Grid.Column="0" Name="Box" Cursor="Hand"
                            MinHeight="{TemplateBinding FontSize}"
                            MinWidth="{TemplateBinding FontSize}"
                            Height="16"
                            Width="16"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Background="{TemplateBinding Background}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                        <Panel>
                            <Border Name="Indicator" CornerRadius="{StaticResource FullCornerRadius}"
                                    Margin="{TemplateBinding BorderThickness}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <Border.Transitions>
                                    <Transitions>
                                        <TransformOperationsTransition Property="RenderTransform"
                                                                       Easing="CubicEaseOut"
                                                                       Duration="{StaticResource ControlInstantAnimationDuration}" />
                                    </Transitions>
                                </Border.Transitions>
                            </Border>
                        </Panel>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter"
                                      Grid.Column="1"
                                      Margin="{TemplateBinding Padding}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      IsVisible="{TemplateBinding Content,
                                                        Converter={x:Static ObjectConverters.IsNotNull}}"
                                      RecognizesAccessKey="True"
                                      TextElement.Foreground="{TemplateBinding Foreground}"
                                      TextElement.FontSize="{TemplateBinding FontSize}"
                                      TextElement.FontWeight="{TemplateBinding FontWeight}" />
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:checked">
            <Style Selector="^ /template/ Border#Box">
                <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(0.6)" />
            </Style>

            <Style Selector="^:pointerover /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(0.7)" />
            </Style>

            <Style Selector="^:pressed /template/ Border#Indicator">
                <Setter Property="RenderTransform" Value="scale(0.5)" />
            </Style>
        </Style>

        <Style Selector="^:unchecked">
            <Style Selector="^:pointerover">
                <Style Selector="^ /template/ Border#Box">
                    <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                </Style>
            </Style>
        </Style>

        <Style Selector="^:disabled /template/ Border#Box">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
