﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="{x:Type PopupRoot}" TargetType="PopupRoot">
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="TransparencyBackgroundFallback" Value="{StaticResource WindowBackgroundBrush}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="Template">
            <ControlTemplate>
                <LayoutTransformControl LayoutTransform="{TemplateBinding Transform}">
                    <Panel>
                        <Border Name="PART_TransparencyFallback"
                                IsHitTestVisible="False" />
                        <VisualLayerManager IsPopup="True">
                            <LayoutTransformControl Name="PART_LayoutTransform"
                                                    LayoutTransform="{TemplateBinding Transform}"
                                                    UseRenderTransform="True">
                                <ContentPresenter Name="PART_ContentPresenter"
                                                  Padding="{TemplateBinding Padding}"
                                                  Background="{TemplateBinding Background}"
                                                  BorderBrush="{TemplateBinding BorderBrush}"
                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                  CornerRadius="{TemplateBinding CornerRadius}"
                                                  ClipToBounds="{TemplateBinding ClipToBounds}"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}" />
                            </LayoutTransformControl>
                        </VisualLayerManager>
                    </Panel>
                </LayoutTransformControl>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
