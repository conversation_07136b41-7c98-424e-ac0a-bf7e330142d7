﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Orientation="Horizontal">
                <husk:StepControl>
                    <husk:StepItem Header="Step 1">
                        <TextBlock Text="Step 1" />
                    </husk:StepItem>
                    <husk:StepItem Header="Step 2">
                        <TextBlock Text="Step 2" />
                    </husk:StepItem>
                    <husk:StepItem Header="Step 3">
                        <TextBlock Text="Step 3" />
                    </husk:StepItem>
                </husk:StepControl>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type husk:StepItem}" TargetType="husk:StepItem">
        <Setter Property="MinWidth" Value="124" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid RowDefinitions="Auto,*" RowSpacing="8">
                    <Grid Grid.Row="0" ColumnDefinitions="*,Auto,*">
                        <husk:SwitchPresenter Grid.Column="0" Height="4"
                                              IsVisible="{TemplateBinding IsFirst, Converter={x:Static BoolConverters.Not}}">
                            <husk:SwitchPresenter.Value>
                                <MultiBinding Converter="{x:Static BoolConverters.Or}">
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="IsSelected" />
                                    <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="IsCompleted" />
                                </MultiBinding>
                            </husk:SwitchPresenter.Value>
                            <husk:SwitchCase Value="{x:True}">
                                <Border Background="{StaticResource ControlAccentBorderBrush}" />
                            </husk:SwitchCase>
                            <husk:SwitchCase Value="{x:False}">
                                <Border Background="{StaticResource ControlBorderBrush}" />
                            </husk:SwitchCase>
                        </husk:SwitchPresenter>
                        <husk:SwitchPresenter Grid.Column="1" Value="{TemplateBinding IsCompleted}" Height="32"
                                              Width="32">
                            <husk:SwitchCase Value="{x:True}">
                                <Border BorderBrush="{StaticResource ControlAccentBorderBrush}"
                                        BorderThickness="2"
                                        Background="{StaticResource ControlAccentBackgroundBrush}"
                                        CornerRadius="{StaticResource FullCornerRadius}">
                                    <fi:SymbolIcon Symbol="Checkmark"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   FontSize="{StaticResource LargeFontSize}" />
                                </Border>
                            </husk:SwitchCase>
                            <husk:SwitchCase Value="{x:False}">
                                <Border BorderBrush="{StaticResource ControlAccentBorderBrush}"
                                        BorderThickness="2" Height="32" Width="32"
                                        CornerRadius="{StaticResource FullCornerRadius}">
                                    <TextBlock
                                        Text="{TemplateBinding Index, Converter={x:Static husk:InternalConverters.ToDisplayIndex}}"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        FontSize="{StaticResource LargeFontSize}" />
                                </Border>
                            </husk:SwitchCase>
                        </husk:SwitchPresenter>
                        <husk:SwitchPresenter Grid.Column="2" Height="4"
                                              IsVisible="{TemplateBinding IsLast, Converter={x:Static BoolConverters.Not}}"
                                              Value="{TemplateBinding IsCompleted}">
                            <husk:SwitchCase Value="{x:True}">
                                <Border Background="{StaticResource ControlAccentBorderBrush}" />
                            </husk:SwitchCase>
                            <husk:SwitchCase Value="{x:False}">
                                <Border Background="{StaticResource ControlBorderBrush}" />
                            </husk:SwitchCase>
                        </husk:SwitchPresenter>
                    </Grid>
                    <Panel Grid.Row="1" VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                        <Border CornerRadius="{TemplateBinding CornerRadius}"
                                Background="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}"
                                IsVisible="{TemplateBinding IsSelected}" />
                        <Grid RowDefinitions="Auto,*" Margin="{TemplateBinding Padding}">
                            <ContentPresenter Grid.Row="0" Content="{TemplateBinding Header}"
                                              ContentTemplate="{TemplateBinding HeaderTemplate}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}">
                                <ContentPresenter.Styles>
                                    <Style Selector="ContentPresenter > TextBlock">
                                        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
                                        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
                                    </Style>
                                </ContentPresenter.Styles>
                            </ContentPresenter>
                            <ContentPresenter Grid.Row="1" Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}">
                                <ContentPresenter.Styles>
                                    <Style Selector="ContentPresenter > TextBlock">
                                        <Setter Property="Foreground"
                                                Value="{StaticResource ControlSecondaryForegroundBrush}" />
                                    </Style>
                                </ContentPresenter.Styles>
                            </ContentPresenter>
                        </Grid>
                    </Panel>
                </Grid>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
