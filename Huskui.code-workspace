{"folders": [{"path": "."}], "settings": {"axaml.selectedSolution": "c:\\Users\\<USER>\\Projects\\Polymerium\\submodules\\Huskui.Avalonia\\Huskui.slnx", "cSpell.words": ["Avalonia", "<PERSON><PERSON><PERSON>"]}, "launch": {"version": "0.2.0", "configurations": [{"name": "C#: Huskui.Gallery Debug", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}/src/Huskui.Gallery/Huskui.Gallery.csproj"}]}}