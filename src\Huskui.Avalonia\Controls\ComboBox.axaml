﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Spacing="12">
                <ComboBox SelectedIndex="0">
                    <ComboBoxItem>
                        <TextBlock Text="Hello World" />
                    </ComboBoxItem>
                </ComboBox>
                <ComboBox PlaceholderText="Placeholder">
                    <ComboBoxItem>
                        <TextBlock Text="Hello World" />
                    </ComboBoxItem>
                </ComboBox>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type ComboBox}" TargetType="ComboBox">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="PlaceholderForeground" Value="{StaticResource ControlSecondaryForegroundBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="MaxDropDownHeight" Value="504" />
        <Setter Property="Background" Value="{StaticResource OverlayInteractiveBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <DataValidationErrors>
                    <Panel>
                        <Border x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}" CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="BorderBrush"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}"
                                                     Easing="SineEaseOut" />
                                    <BrushTransition Property="Background"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}"
                                                     Easing="SineEaseOut" />
                                </Transitions>
                            </Border.Transitions>

                            <Grid Margin="{TemplateBinding Padding}" ColumnDefinitions="*,6,Auto">
                                <Panel Grid.Column="0">
                                    <TextBlock Name="PlaceholderTextBlock"
                                               HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                               VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                               Foreground="{TemplateBinding PlaceholderForeground}"
                                               TextTrimming="CharacterEllipsis"
                                               IsVisible="{TemplateBinding SelectionBoxItem,
                                                   Converter={x:Static ObjectConverters.IsNull}}"
                                               Text="{TemplateBinding PlaceholderText}" />
                                    <ContentPresenter Name="PART_ContentPresenter"
                                                      Content="{TemplateBinding SelectionBoxItem}"
                                                      ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                                </Panel>
                                <fi:SymbolIcon Grid.Column="2" Symbol="ChevronUpDown"
                                               FontSize="{TemplateBinding FontSize}" />
                            </Grid>
                        </Border>
                        <Border x:Name="GlowBorder" CornerRadius="{TemplateBinding CornerRadius}"
                                BorderBrush="{StaticResource TransparentBrush}" BorderThickness="2">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="BorderBrush"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}"
                                                     Easing="SineEaseOut" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                        <Popup Name="PART_Popup"
                               MinWidth="{Binding Bounds.Width, RelativeSource={RelativeSource TemplatedParent}}"
                               MinHeight="12"
                               MaxHeight="{TemplateBinding MaxDropDownHeight}"
                               IsLightDismissEnabled="True"
                               IsOpen="{TemplateBinding IsDropDownOpen,Mode=TwoWay}"
                               PlacementTarget="{TemplateBinding}"
                               InheritsTransform="True">
                            <Border Margin="2,4" Background="{StaticResource FlyoutBackgroundBrush}"
                                    CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True">
                                <Border.Effect>
                                    <DropShadowEffect OffsetX="0" OffsetY="0" Opacity="0.2" />
                                </Border.Effect>
                                <ScrollViewer
                                    HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                    IsDeferredScrollingEnabled="{TemplateBinding (ScrollViewer.IsDeferredScrollingEnabled)}">
                                    <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Panel>
                </DataValidationErrors>
            </ControlTemplate>
        </Setter>


        <Style Selector="^:focus:not(:dropdownopen) /template/ Border#GlowBorder">
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:pointerover /template/ Border#Border">
            <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type ComboBoxItem}" TargetType="ComboBoxItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Border" CornerRadius="{TemplateBinding CornerRadius}"
                        ClipToBounds="{TemplateBinding ClipToBounds}" Margin="2"
                        Background="{TemplateBinding Background}" BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <Border.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Border.Transitions>
                    <Grid ColumnDefinitions="*,6,Auto" Margin="{TemplateBinding Padding}">
                        <ContentPresenter Grid.Column="0" Name="PART_ContentPresenter"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                          RenderTransform="{x:Null}"
                                          HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}">
                            <ContentPresenter.Transitions>
                                <Transitions>
                                    <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                                   Duration="{StaticResource ControlFastestAnimationDuration}" />
                                </Transitions>
                            </ContentPresenter.Transitions>
                        </ContentPresenter>
                        <fi:SymbolIcon Grid.Column="2" Name="Mark" Symbol="Checkmark"
                                       FontSize="{TemplateBinding FontSize}"
                                       HorizontalAlignment="Right" VerticalAlignment="Center" IsVisible="False"
                                       Foreground="{StaticResource ControlAccentForegroundBrush}" />
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover:not(:selected)">
            <Style Selector="^ /template/ Border#Border">
                <Setter Property="Background" Value="{StaticResource OverlayHalfBackgroundBrush}" />
            </Style>

            <Style Selector="^ /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="RenderTransform" Value="translateX(8px)" />
            </Style>
        </Style>

        <Style Selector="^:selected /template/ Border#Border">
            <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
            <Setter Property="Margin" Value="0" />
            <Setter Property="CornerRadius" Value="0" />
        </Style>

        <Style Selector="^:selected /template/ fi|SymbolIcon#Mark">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="GhostComboBoxTheme" TargetType="ComboBox">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="PlaceholderForeground" Value="{StaticResource ControlSecondaryForegroundBrush}" />
        <Setter Property="MaxDropDownHeight" Value="504" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <DataValidationErrors>
                    <Panel>
                        <Border x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}" CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="BorderBrush"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}"
                                                     Easing="SineEaseOut" />
                                    <BrushTransition Property="Background"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}"
                                                     Easing="SineEaseOut" />
                                </Transitions>
                            </Border.Transitions>

                            <Grid Margin="{TemplateBinding Padding}" ColumnDefinitions="*,6,Auto">
                                <Panel Grid.Column="0">
                                    <TextBlock Name="PlaceholderTextBlock"
                                               HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                               VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                               Foreground="{TemplateBinding PlaceholderForeground}"
                                               TextTrimming="CharacterEllipsis"
                                               IsVisible="{TemplateBinding SelectionBoxItem,
                                                   Converter={x:Static ObjectConverters.IsNull}}"
                                               Text="{TemplateBinding PlaceholderText}" />
                                    <ContentPresenter Name="PART_ContentPresenter"
                                                      Content="{TemplateBinding SelectionBoxItem}"
                                                      ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                                </Panel>
                                <fi:SymbolIcon Grid.Column="2" Symbol="ChevronUpDown"
                                               FontSize="{TemplateBinding FontSize}" />
                            </Grid>
                        </Border>
                        <Popup Name="PART_Popup"
                               MinWidth="{Binding Bounds.Width, RelativeSource={RelativeSource TemplatedParent}}"
                               MaxHeight="{TemplateBinding MaxDropDownHeight}"
                               IsLightDismissEnabled="True"
                               IsOpen="{TemplateBinding IsDropDownOpen,Mode=TwoWay}"
                               PlacementTarget="{TemplateBinding}"
                               InheritsTransform="True">
                            <Border Margin="2,4" Background="{StaticResource FlyoutBackgroundBrush}"
                                    CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True">
                                <Border.Effect>
                                    <DropShadowEffect OffsetX="0" OffsetY="0" Opacity="0.2" />
                                </Border.Effect>
                                <ScrollViewer
                                    HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                    IsDeferredScrollingEnabled="{TemplateBinding (ScrollViewer.IsDeferredScrollingEnabled)}">
                                    <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Panel>
                </DataValidationErrors>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#Border">
            <Setter Property="Background" Value="{StaticResource OverlayHalfBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
