﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="{x:Type ScrollViewer}"
                  TargetType="ScrollViewer">
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid RowDefinitions="*,Auto" ColumnDefinitions="*,Auto">
                    <ScrollContentPresenter Grid.Row="0" Grid.Column="0" Grid.RowSpan="2" Grid.ColumnSpan="2"
                                            Name="PART_ContentPresenter"
                                            Padding="{TemplateBinding Padding}"
                                            ClipToBounds="{TemplateBinding ClipToBounds}"
                                            HorizontalSnapPointsType="{TemplateBinding HorizontalSnapPointsType}"
                                            VerticalSnapPointsType="{TemplateBinding VerticalSnapPointsType}"
                                            HorizontalSnapPointsAlignment="{TemplateBinding HorizontalSnapPointsAlignment}"
                                            VerticalSnapPointsAlignment="{TemplateBinding VerticalSnapPointsAlignment}"
                                            Background="{TemplateBinding Background}"
                                            ScrollViewer.IsScrollInertiaEnabled="{TemplateBinding IsScrollInertiaEnabled}"
                                            ScrollViewer.BringIntoViewOnFocusChange="{TemplateBinding BringIntoViewOnFocusChange}">
                        <ScrollContentPresenter.GestureRecognizers>
                            <ScrollGestureRecognizer
                                CanHorizontallyScroll="{Binding CanHorizontallyScroll, ElementName=PART_ContentPresenter}"
                                CanVerticallyScroll="{Binding CanVerticallyScroll, ElementName=PART_ContentPresenter}"
                                IsScrollInertiaEnabled="{Binding (ScrollViewer.IsScrollInertiaEnabled), ElementName=PART_ContentPresenter}" />
                        </ScrollContentPresenter.GestureRecognizers>
                    </ScrollContentPresenter>
                    <ScrollBar Grid.Row="1" Grid.Column="0" Name="PART_HorizontalScrollBar"
                               Orientation="Horizontal">
                        <ScrollBar.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </ScrollBar.Transitions>
                    </ScrollBar>
                    <ScrollBar Grid.Row="0" Grid.Column="1" Name="PART_VerticalScrollBar"
                               Orientation="Vertical">
                        <ScrollBar.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </ScrollBar.Transitions>
                    </ScrollBar>
                    <Panel Name="PART_ScrollBarsSeparator"
                           Grid.Row="1"
                           Grid.Column="1"
                           Background="Transparent" />
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^[AllowAutoHide=True]:not(:pointerover) /template/ ScrollBar:not(:focus-within)">
            <Setter Property="Opacity" Value="0" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
