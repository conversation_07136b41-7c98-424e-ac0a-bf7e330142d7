﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <local:SwitchPresenter Value="2" TargetType="x:Int32" Margin="24">
                <local:SwitchCase Value="1">1</local:SwitchCase>
                <local:SwitchCase Value="2">222</local:SwitchCase>
                <local:SwitchCase Value="3">3</local:SwitchCase>
            </local:SwitchPresenter>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:SwitchPresenter}" TargetType="local:SwitchPresenter">
        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter Content="{TemplateBinding Content}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
