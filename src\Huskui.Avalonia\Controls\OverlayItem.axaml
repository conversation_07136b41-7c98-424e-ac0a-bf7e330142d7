﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:OverlayItem}" TargetType="local:OverlayItem">
        <Setter Property="RenderTransformOrigin" Value="50%,50%" />
        <Setter Property="VerticalAlignment" Value="{Binding $parent[local:OverlayHost].VerticalContentAlignment}" />
        <Setter Property="HorizontalAlignment" Value="{Binding $parent[local:OverlayHost].HorizontalContentAlignment}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Template">
            <ControlTemplate>
                <LayoutTransformControl Name="TransformRoot" UseRenderTransform="True">
                    <LayoutTransformControl.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <TransformOperationsTransition Property="RenderTransform"
                                                           Delay="{StaticResource ControlFastestAnimationDuration}"
                                                           Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </LayoutTransformControl.Transitions>
                    <ContentPresenter Name="{x:Static local:OverlayItem.PART_ContentPresenter}"
                                      Content="{TemplateBinding Content}"
                                      Background="{TemplateBinding Background}"
                                      Padding="{TemplateBinding Padding}"
                                      BackgroundSizing="{TemplateBinding BackgroundSizing}" />
                </LayoutTransformControl>
            </ControlTemplate>
        </Setter>

        <!-- <Style Selector="^:active /template/ LayoutTransformControl#TransformRoot"> -->
        <!--     <Setter Property="Effect"> -->
        <!--         <DropShadowEffect OffsetX="0" OffsetY="0" BlurRadius="12" Opacity="0.2" /> -->
        <!--     </Setter> -->
        <!-- </Style>      -->

        <Style Selector="^:not(:active) /template/ LayoutTransformControl#TransformRoot">
            <Setter Property="Effect">
                <BlurEffect Radius="2" />
            </Setter>
        </Style>

        <Style Selector="^">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="Opacity" Value="0.0" />
                <Setter Property="RenderTransform" Value="translateY(-30px) scaleX(0.90)" />
            </Style>
        </Style>

        <Style Selector="^[Distance=0]">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="Opacity" Value="1.0" />
                <Setter Property="RenderTransform" Value="translateY(0px) scaleX(1.0)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=1]">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="IsEnabled" Value="False" />
                <Setter Property="Opacity" Value="0.8" />
                <Setter Property="RenderTransform" Value="translateY(-6px) scaleX(0.98)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=2]">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="IsEnabled" Value="False" />
                <Setter Property="Opacity" Value="0.6" />
                <Setter Property="RenderTransform" Value="translateY(-12px) scaleX(0.96)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=3]">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="IsEnabled" Value="False" />
                <Setter Property="Opacity" Value="0.4" />
                <Setter Property="RenderTransform" Value="translateY(-18px) scaleX(0.94)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=4]">
            <Style Selector="^ /template/ LayoutTransformControl#TransformRoot">
                <Setter Property="IsEnabled" Value="False" />
                <Setter Property="Opacity" Value="0.2" />
                <Setter Property="RenderTransform" Value="translateY(-24px) scaleX(0.92)" />
            </Style>
        </Style>
    </ControlTheme>
</ResourceDictionary>
