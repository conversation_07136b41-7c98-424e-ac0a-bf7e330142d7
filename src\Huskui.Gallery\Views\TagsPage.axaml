﻿<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      x:Class="Huskui.Gallery.Views.TagsPage">
    <StackPanel Spacing="32">

        <controls:ExampleContainer Title="Basic Tags"
                                   Description="Tags with different styles and colors">
            <controls:ExampleContainer.Controls>
                <StackPanel Spacing="12">
                    <TextBlock Text="Tag Styles" FontWeight="Bold" FontSize="14" />
                    <ToggleButton x:Name="SmallSizeToggle" Content="Small Size" />
                    <ToggleButton x:Name="StatusStyleToggle" Content="Status Style" />
                    <TextBlock Text="Toggle different tag styles to see visual variations."
                               TextWrapping="Wrap"
                               FontSize="12"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </controls:ExampleContainer.Controls>

            <StackPanel Spacing="16">
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <husk:Tag Content="Default"
                              Classes.Small="{Binding #SmallSizeToggle.IsChecked}"
                              Classes.Status="{Binding #StatusStyleToggle.IsChecked}" />
                    <husk:Tag Content="Primary"
                              Classes="Primary"
                              Classes.Small="{Binding #SmallSizeToggle.IsChecked}"
                              Classes.Status="{Binding #StatusStyleToggle.IsChecked}" />
                    <husk:Tag Content="Success"
                              Classes="Success"
                              Classes.Small="{Binding #SmallSizeToggle.IsChecked}"
                              Classes.Status="{Binding #StatusStyleToggle.IsChecked}" />
                    <husk:Tag Content="Warning"
                              Classes="Warning"
                              Classes.Small="{Binding #SmallSizeToggle.IsChecked}"
                              Classes.Status="{Binding #StatusStyleToggle.IsChecked}" />
                    <husk:Tag Content="Danger"
                              Classes="Danger"
                              Classes.Small="{Binding #SmallSizeToggle.IsChecked}"
                              Classes.Status="{Binding #StatusStyleToggle.IsChecked}" />
                </StackPanel>

                <StackPanel Spacing="8">
                    <TextBlock Text="Tag Collection Example" FontWeight="Bold" />
                    <WrapPanel>
                        <husk:Tag Content="C#" Classes="Primary" Margin="2" />
                        <husk:Tag Content="Avalonia" Classes="Success" Margin="2" />
                        <husk:Tag Content="MVVM" Classes="Warning" Margin="2" />
                        <husk:Tag Content="Cross-platform" Margin="2" />
                        <husk:Tag Content="Open Source" Classes="Primary" Margin="2" />
                        <husk:Tag Content="UI Framework" Classes="Success" Margin="2" />
                    </WrapPanel>
                </StackPanel>
            </StackPanel>
        </controls:ExampleContainer>
    </StackPanel>
</controls:ControlPage>
