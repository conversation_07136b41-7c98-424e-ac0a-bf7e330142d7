﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="using:Huskui.Avalonia.Controls">

    <Thickness x:Key="CardContentMargin">12</Thickness>

    <ControlTheme x:Key="{x:Type local:Card}" TargetType="local:Card">
        <Setter Property="CornerRadius" Value="{StaticResource LargeCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="{StaticResource CardContentMargin}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}" CornerRadius="{TemplateBinding CornerRadius}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}" BoxShadow="{TemplateBinding BoxShadow}">
                    <ContentPresenter Margin="{TemplateBinding Padding}" Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
