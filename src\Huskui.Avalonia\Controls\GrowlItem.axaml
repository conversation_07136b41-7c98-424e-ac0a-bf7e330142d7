﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="using:FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Width="360">
            <ScrollViewer>
                <StackPanel Margin="24" Spacing="6">
                    <local:GrowlItem Title="{x:Null}" IsCloseButtonVisible="False">
                        <Button Content="Do NOT Press" />
                    </local:GrowlItem>
                    <local:GrowlItem Level="Success" Title="Downloading file"
                                     IsProgressBarVisible="True" Progress="54">
                        <local:GrowlItem.Actions>
                            <local:GrowlAction Text="Cancel" />
                        </local:GrowlItem.Actions>
                        <StackPanel Spacing="4">
                            <StackPanel Orientation="Horizontal" Spacing="6">
                                <fi:SymbolIcon Symbol="FolderZip" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="四大名著.zip" />
                            </StackPanel>
                            <TextBlock Foreground="{StaticResource ControlSecondaryForegroundBrush}">
                                <Run Text="D: 128 B/s" />
                                <Run Text="E: 46 s" />
                                <Run Text="S: 5.4 MiB" />
                            </TextBlock>
                        </StackPanel>
                    </local:GrowlItem>
                    <local:GrowlItem Level="Warning" Title="Checking updates" Content="It takes very long."
                                     IsProgressBarVisible="True" IsProgressIndeterminate="True">
                        <local:GrowlItem.Actions>
                            <local:GrowlAction Text="Go" />
                            <local:GrowlAction Text="To" />
                            <local:GrowlAction Text="Hell" />
                        </local:GrowlItem.Actions>
                    </local:GrowlItem>
                    <local:GrowlItem Level="Danger" Content="It takes...shot.">
                        <local:GrowlItem.Actions>
                            <local:GrowlAction Text="Then" />
                            <local:GrowlAction Text="Go" />
                            <local:GrowlAction Text="Back" />
                        </local:GrowlItem.Actions>
                    </local:GrowlItem>
                </StackPanel>
            </ScrollViewer>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:GrowlItem}" TargetType="local:GrowlItem">
        <Setter Property="Level" Value="Information" />
        <Setter Property="Title" Value="Information" />
        <Setter Property="IsCloseButtonVisible" Value="True" />
        <Setter Property="HorizontalAlignment"
                Value="{Binding HorizontalContentAlignment,RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=local:GrowlHost}}" />
        <Setter Property="VerticalAlignment"
                Value="{Binding VerticalContentAlignment,RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=local:GrowlHost}}" />
        <Setter Property="BorderBrush" Value="{StaticResource GrowlBorderBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource GrowlBackgroundBrush}" />
        <Setter Property="BorderThickness" Value="3" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="MinWidth" Value="220" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="{StaticResource ControlNormalAnimationDuration}"
                                 Easing="CubicEaseOut" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border Name="Border" ClipToBounds="False"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Background="{TemplateBinding Background}"
                            BackgroundSizing="{TemplateBinding BackgroundSizing}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Margin="3" BoxShadow="0 0 4 0 #3F000000">
                        <Border CornerRadius="{TemplateBinding CornerRadius}"
                                ClipToBounds="{TemplateBinding ClipToBounds}">
                            <DockPanel>
                                <ProgressBar DockPanel.Dock="Bottom" CornerRadius="0"
                                             Value="{TemplateBinding Progress}"
                                             IsIndeterminate="{TemplateBinding IsProgressIndeterminate}"
                                             Maximum="{TemplateBinding ProgressMaximum}"
                                             IsVisible="{TemplateBinding IsProgressBarVisible}" />
                                <Grid RowDefinitions="Auto,4,*,4,Auto" ColumnDefinitions="*,Auto"
                                      Margin="{TemplateBinding Padding}">
                                    <Grid Grid.Row="0" Grid.ColumnSpan="2" Grid.Column="0"
                                          ColumnDefinitions="*,Auto" ColumnSpacing="8">
                                        <Grid.IsVisible>
                                            <MultiBinding Converter="{x:Static BoolConverters.Or}">
                                                <Binding Path="Title"
                                                         RelativeSource="{RelativeSource Mode=TemplatedParent}"
                                                         Converter="{x:Static ObjectConverters.IsNotNull}" />
                                                <Binding Path="IsCloseButtonVisible"
                                                         RelativeSource="{RelativeSource Mode=TemplatedParent}" />
                                            </MultiBinding>
                                        </Grid.IsVisible>
                                        <TextBlock Grid.Column="0" Text="{TemplateBinding Title}"
                                                   FontWeight="{StaticResource ControlStrongFontWeight}"
                                                   TextTrimming="CharacterEllipsis" />
                                        <Button Grid.Column="1" Padding="6"
                                                CornerRadius="{StaticResource FullCornerRadius}"
                                                Theme="{StaticResource OutlineButtonTheme}"
                                                Command="{Binding DismissCommand,RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                                IsVisible="{TemplateBinding IsCloseButtonVisible}">
                                            <fi:SymbolIcon Symbol="Dismiss" Height="6" Width="6" />
                                        </Button>
                                    </Grid>
                                    <ContentPresenter Grid.Row="2" Grid.ColumnSpan="2" Grid.Column="0"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}">
                                        <ContentPresenter.Styles>
                                            <Style Selector="ContentPresenter > TextBlock">
                                                <Setter Property="TextWrapping" Value="Wrap" />
                                                <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                                                <Setter Property="Foreground"
                                                        Value="{StaticResource ControlSecondaryForegroundBrush}" />
                                            </Style>
                                        </ContentPresenter.Styles>
                                    </ContentPresenter>
                                    <ItemsControl Grid.Row="4" Grid.Column="1"
                                                  ItemsSource="{TemplateBinding Actions}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Horizontal" Spacing="12" />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate DataType="local:GrowlAction">
                                                <HyperlinkButton Content="{Binding Text}"
                                                                 Command="{Binding Command}"
                                                                 CommandParameter="{Binding  Parameter}" />
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Grid>
                            </DockPanel>
                        </Border>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:success">
            <Setter Property="Title" Value="Success" />
            <Setter Property="Background" Value="{StaticResource GrowlSuccessBackgroundBrush}" />
        </Style>
        <Style Selector="^:warning">
            <Setter Property="Title" Value="Warning" />
            <Setter Property="Background" Value="{StaticResource GrowlWarningBackgroundBrush}" />
        </Style>
        <Style Selector="^:danger">
            <Setter Property="Title" Value="Danger" />
            <Setter Property="Background" Value="{StaticResource GrowlDangerBackgroundBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
