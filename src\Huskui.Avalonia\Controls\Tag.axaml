﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">

    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="12" HorizontalAlignment="Center" Spacing="8">
                <local:Tag Content="Hello" />
                <local:Tag Content="世界" />
                <local:Tag Content="Primary" Classes="Primary" />
                <local:Tag Classes="Success">
                    <StackPanel Orientation="Horizontal" Spacing="4">
                        <Ellipse Width="6" Height="6" Fill="{Binding $parent[local:Tag].Foreground}" />
                        <TextBlock Text="Success" />
                    </StackPanel>
                </local:Tag>
                <local:Tag Content="Warning" Classes="Warning" />
                <local:Tag Content="Danger" Classes="Danger" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type local:Tag}" TargetType="local:Tag">
        <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlTranslucentForegroundBrush}" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Padding" Value="6,2" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="PART_Border" CornerRadius="{TemplateBinding CornerRadius}"
                        Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"
                        ClipToBounds="True">
                    <ContentPresenter Content="{TemplateBinding Content}" Padding="{TemplateBinding Padding}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^.Primary">
            <Setter Property="Foreground" Value="{StaticResource ControlAccentTranslucentForegroundBrush}" />

            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
            </Style>
        </Style>
        <Style Selector="^.Success">
            <Setter Property="Foreground" Value="{StaticResource ControlSuccessTranslucentForegroundBrush}" />

            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{StaticResource ControlSuccessTranslucentFullBackgroundBrush}" />
            </Style>
        </Style>
        <Style Selector="^.Warning">
            <Setter Property="Foreground" Value="{StaticResource ControlWarningTranslucentForegroundBrush}" />

            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{StaticResource ControlWarningTranslucentFullBackgroundBrush}" />
            </Style>
        </Style>
        <Style Selector="^.Danger">
            <Setter Property="Foreground" Value="{StaticResource ControlDangerTranslucentForegroundBrush}" />

            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{StaticResource ControlDangerTranslucentFullBackgroundBrush}" />
            </Style>
        </Style>
        <Style Selector="^.Status">
            <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
        </Style>
        <Style Selector="^.Small">
            <Setter Property="Padding" Value="4,2" />
            <Setter Property="FontSize" Value="{StaticResource ExtraSmallFontSize}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
