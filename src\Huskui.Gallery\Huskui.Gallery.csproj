﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <LangVersion>preview</LangVersion>
        <AssemblyTitle>Huskui Gallery</AssemblyTitle>
        <AssemblyDescription>A comprehensive gallery showcasing Huskui.Avalonia controls</AssemblyDescription>
        <ApplicationIcon>Assets\Icon.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.6"/>
        <PackageReference Include="Avalonia.Desktop" Version="11.3.6"/>
        <PackageReference Include="Avalonia.Diagnostics" Version="11.3.6">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0"/>
        <PackageReference Include="FluentIcons.Avalonia" Version="1.1.310"/>
        <PackageReference Include="HotAvalonia" Version="3.0.0">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.9"/>
        <PackageReference Include="DynamicData" Version="9.4.1"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Huskui.Avalonia\Huskui.Avalonia.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>
</Project>
