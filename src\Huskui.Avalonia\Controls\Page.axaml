﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">

    <Thickness x:Key="PageContentMargin">16,56,16,16</Thickness>
    <Thickness x:Key="PageHeaderlessContentMargin">16</Thickness>
    <Thickness x:Key="PageToplessContentMargin">16,0,16,16</Thickness>
    <Thickness x:Key="PageBottomlessContentMargin">16,56,16,0</Thickness>
    <Thickness x:Key="PageToplessBottomlessContentMargin">16,0,16,0</Thickness>

    <ControlTheme x:Key="PageHeaderTextBlockTheme" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Effect">
            <DropShadowEffect Opacity="0.1" BlurRadius="6" />
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type local:Page}" TargetType="local:Page">
        <Setter Property="Padding" Value="{StaticResource PageContentMargin}" />
        <Setter Property="CanGoBack" Value="{Binding $parent[local:Frame].CanGoBack, Mode=OneWay}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border CornerRadius="{TemplateBinding CornerRadius}"
                        Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        BoxShadow="{TemplateBinding BoxShadow}" ClipToBounds="True">
                    <ScrollViewer
                        VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                        HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                        IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                        IsScrollChainingEnabled="{TemplateBinding ScrollViewer.IsScrollChainingEnabled}"
                        IsScrollInertiaEnabled="{TemplateBinding ScrollViewer.IsScrollInertiaEnabled}">
                        <Panel>
                            <ContentPresenter Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                            <DockPanel VerticalAlignment="Top" Margin="12"
                                       IsVisible="{TemplateBinding IsHeaderVisible}">
                                <Button DockPanel.Dock="Left" CornerRadius="{StaticResource SmallCornerRadius}"
                                        Margin="0,0,12,0"
                                        Command="{Binding $parent[local:Frame].GoBackCommand}"
                                        IsVisible="{TemplateBinding IsBackButtonVisible}">
                                    <fi:SymbolIcon Symbol="ArrowLeft" FontSize="{StaticResource MediumFontSize}"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center" />
                                </Button>
                                <ContentPresenter Content="{TemplateBinding Header}"
                                                  ContentTemplate="{TemplateBinding HeaderTemplate}">
                                    <ContentPresenter.Styles>
                                        <Style Selector="ContentPresenter > TextBlock">
                                            <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
                                            <Setter Property="FontWeight"
                                                    Value="{StaticResource ControlStrongFontWeight}" />
                                            <Setter Property="VerticalAlignment" Value="Center" />
                                            <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                                            <Setter Property="Effect">
                                                <DropShadowEffect Opacity="0.1" BlurRadius="6" />
                                            </Setter>
                                        </Style>
                                    </ContentPresenter.Styles>
                                </ContentPresenter>
                            </DockPanel>
                        </Panel>
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
