<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
                      x:Class="Huskui.Gallery.Views.DataGridsPage">

    <StackPanel Spacing="32">

        <!-- ParkUI Table Plain Variant -->
        <controls:ExampleContainer Title="Plain Table (Default)"
                                   Description="Clean table design with subtle hover effects, following ParkUI's plain table variant">
            <DataGrid Width="700" Height="300"
                      AutoGenerateColumns="False"
                      CanUserResizeColumns="True"
                      CanUserSortColumns="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Width="100" Header="ID" />
                    <DataGridTextColumn Width="200" Header="Product Name" />
                    <DataGridTextColumn Width="100" Header="Category" />
                    <DataGridTextColumn Width="80" Header="Stock" />
                    <DataGridTextColumn Width="120" Header="Price" />
                </DataGrid.Columns>
            </DataGrid>
        </controls:ExampleContainer>

        <!-- ParkUI Table Outline Variant -->
        <controls:ExampleContainer Title="Outline Table"
                                   Description="Table with border and subtle header background, following ParkUI's outline table variant">
            <DataGrid Classes="Outline" Width="700" Height="300"
                      AutoGenerateColumns="False"
                      CanUserResizeColumns="True"
                      CanUserSortColumns="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Width="100" Header="ID" />
                    <DataGridTextColumn Width="200" Header="Product Name" />
                    <DataGridTextColumn Width="100" Header="Category" />
                    <DataGridTextColumn Width="80" Header="Stock" />
                    <DataGridTextColumn Width="120" Header="Price" />
                </DataGrid.Columns>
            </DataGrid>
        </controls:ExampleContainer>

    </StackPanel>

</controls:ControlPage>
