<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:Dialog}" TargetType="local:Dialog">
        <Setter Property="Container" Value="{Binding $parent[local:OverlayItem], Mode=OneWay} " />
        <Setter Property="MinHeight" Value="128" />
        <Setter Property="MinWidth" Value="256" />
        <Setter Property="Background" Value="{StaticResource FlyoutBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource LargeCornerRadius}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="PrimaryText" Value="Confirm" />
        <Setter Property="SecondaryText" Value="Cancel" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="Margin" Value="24" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <Grid RowDefinitions="Auto,Auto,*,Auto">
                        <ContentPresenter Grid.Row="0" Name="PART_HeaderPresenter"
                                          Content="{TemplateBinding Header}"
                                          ContentTemplate="{TemplateBinding HeaderTemplate}" />
                        <StackPanel Grid.Row="1" Spacing="4" Margin="18,18,18,0">
                            <TextBlock Text="{TemplateBinding Title}"
                                       IsVisible="{Binding Title,RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                                       FontSize="{StaticResource LargeFontSize}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock Text="{TemplateBinding Message}"
                                       IsVisible="{Binding Message,RelativeSource={RelativeSource Mode=TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       TextWrapping="WrapWithOverflow"
                                       MaxWidth="{Binding #PART_ContentPresenter.Bounds.Width}"
                                       FontSize="{StaticResource SmallFontSize}" />
                        </StackPanel>
                        <ContentPresenter Grid.Row="2" Name="PART_ContentPresenter"
                                          Margin="18,18,18,0" Foreground="{TemplateBinding Foreground}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                        <Grid Grid.Row="3" ColumnDefinitions="*,*" Margin="18" ColumnSpacing="8">
                            <Button Grid.Column="0" Classes="Primary" IsDefault="True"
                                    Command="{Binding PrimaryCommand, RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                    IsVisible="{TemplateBinding IsPrimaryButtonVisible}">
                                <TextBlock Text="{TemplateBinding PrimaryText}" />
                            </Button>
                            <Button IsCancel="True"
                                    Grid.Column="{TemplateBinding IsPrimaryButtonVisible, Converter={x:Static local:InternalConverters.ButtonColumn},ConverterParameter=1}"
                                    Grid.ColumnSpan="{TemplateBinding IsPrimaryButtonVisible, Converter={x:Static local:InternalConverters.ButtonColumnSpan},ConverterParameter=2}"
                                    Command="{Binding SecondaryCommand, RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                <TextBlock Text="{TemplateBinding SecondaryText}" />
                            </Button>
                        </Grid>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
