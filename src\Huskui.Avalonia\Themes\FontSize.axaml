﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Panel>
            <Grid Margin="24" RowDefinitions="*,12,*">
                <StackPanel Grid.Row="0">
                    <TextBlock FontSize="{StaticResource ExtraSmallFontSize}" Text="Extra Small Font Size 超小等大小字体" />
                    <TextBlock FontSize="{StaticResource SmallFontSize}" Text="Small Font Size 小等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource LargeFontSize}" Text="Large Font Size 大等大小字体" />
                    <TextBlock FontSize="{StaticResource ExtraLargeFontSize}" Text="Extra Large Font Size 超大等大小字体" />
                </StackPanel>
                <StackPanel Grid.Row="2">
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Thin"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="ExtraLight"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Light"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="SemiLight"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Regular"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Medium"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="SemiBold"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="DemiBold"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Bold"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="ExtraBold"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="Black"
                               Text="Medium Font Size 中等大小字体" />
                    <TextBlock FontSize="{StaticResource MediumFontSize}" FontWeight="SemiBold"
                               Text="Medium Font Size 中等大小字体" />
                </StackPanel>
            </Grid>
        </Panel>
    </Design.PreviewWith>

    <ResourceDictionary.MergedDictionaries>
        <!-- Quicksand 的字形要求普通大小为 14 才看得清, 不过能用 Medium 字重补偿 -->
        <!-- Outfit 则只需要 12 就能很好显示 -->
        <MergeResourceInclude Source="/Themes/FontSize.Large.axaml" />
    </ResourceDictionary.MergedDictionaries>

</ResourceDictionary>
