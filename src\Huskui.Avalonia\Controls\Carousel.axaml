﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Spacing="12" Margin="24">
                <Carousel Height="160" Width="260">
                    <Carousel.Items>
                        <Border Background="Aqua" />
                        <Border Background="Magenta" />
                        <Border Background="Yellow" />
                    </Carousel.Items>
                </Carousel>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type Carousel}" TargetType="Carousel">
        <Setter Property="PageTransition">
            <PageSlide SlideInEasing="CubicEaseOut" SlideOutEasing="CubicEaseIn"
                       Duration="{StaticResource ControlFasterAnimationDuration}" />
        </Setter>
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}" ClipToBounds="{TemplateBinding ClipToBounds}">
                    <ScrollViewer
                        Name="PART_ScrollViewer"
                        BringIntoViewOnFocusChange="False"
                        HorizontalScrollBarVisibility="Hidden"
                        VerticalScrollBarVisibility="Hidden">
                        <ItemsPresenter Name="PART_ItemsPresenter"
                                        ItemsPanel="{TemplateBinding ItemsPanel}"
                                        Margin="{TemplateBinding Padding}" />
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover">
            <Style Selector="^ /template/ Grid#ControlPanel">
                <Setter Property="Opacity" Value="1" />
            </Style>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="SimpleCarouselTheme" TargetType="Carousel">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}" ClipToBounds="{TemplateBinding ClipToBounds}">
                    <ScrollViewer Name="PART_ScrollViewer"
                                  BringIntoViewOnFocusChange="False"
                                  HorizontalScrollBarVisibility="Hidden"
                                  VerticalScrollBarVisibility="Hidden">
                        <ItemsPresenter Name="PART_ItemsPresenter"
                                        ItemsPanel="{TemplateBinding ItemsPanel}"
                                        Margin="{TemplateBinding Padding}" />
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
