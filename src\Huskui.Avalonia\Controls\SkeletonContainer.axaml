﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Height="328" Width="256">
            <Grid Margin="24" RowDefinitions="*,8,*,8,*">
                <local:SkeletonContainer Grid.Row="0">
                    <Button>Hello World</Button>
                </local:SkeletonContainer>
                <local:SkeletonContainer Grid.Row="2" IsLoading="True">
                    <Button>Hello World</Button>
                </local:SkeletonContainer>
                <local:SkeletonContainer Grid.Row="4" IsLoading="True" IsAnimated="False">
                    <Button>Hello World</Button>
                </local:SkeletonContainer>
            </Grid>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:SkeletonContainer}" TargetType="local:SkeletonContainer">
        <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <ContentPresenter Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}" />
                    <Border Name="Mask" Background="{TemplateBinding Background}" IsVisible="False"
                            CornerRadius="{TemplateBinding CornerRadius}" ClipToBounds="True">
                        <Border Name="Wave" Background="{StaticResource Overlay7Color}"
                                Opacity="0" />
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:loading /template/ Border#Mask">
            <Setter Property="IsVisible" Value="True" />
        </Style>
        <Style Selector="^:loading:animated /template/ Border#Wave">
            <Style.Animations>
                <Animation Duration="0:0:0.8" PlaybackDirection="Alternate" IterationCount="Infinite"
                           Easing="SineEaseOut">
                    <KeyFrame Cue="0%">
                        <Setter Property="Opacity" Value="0" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="Opacity" Value="1" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </ControlTheme>
</ResourceDictionary>
