﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="Huskui.Avalonia.HuskuiTheme">
    <Styles.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="/Themes/CornerRadius.Normal.axaml" />
                <ResourceInclude Source="/Themes/Colors.Accent.Neutral.axaml" />
                <ResourceInclude Source="/Themes/Colors.axaml" />
                <ResourceInclude Source="/Themes/Basics.axaml" />
                <ResourceInclude Source="/Themes/FontSize.axaml" />
                <ResourceInclude Source="/Themes/Controls.axaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Styles.Resources>

    <StyleInclude Source="/Themes/Animations.axaml" />
    <StyleInclude Source="/Themes/Miscellaneous.axaml" />
</Styles>
