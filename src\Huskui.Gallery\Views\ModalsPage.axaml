﻿<controls:ControlPage xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                      xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                      mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                      x:Class="Huskui.Gallery.Views.ModalsPage">
    <StackPanel Spacing="32">

        <controls:ExampleContainer Title="Long Interaction Modals"
                                   Description="Modal containers for complex, extended user interactions">
            <controls:ExampleContainer.Controls>
                <StackPanel Spacing="12">
                    <TextBlock Text="Long Interaction Controls" FontWeight="Bold" FontSize="14" />
                    <Button Content="User Settings" Click="OnShowSettingsModalClick" />
                    <Button Content="User Profile" Click="OnShowProfileModalClick" />
                    <TextBlock
                        Text="Click buttons to show modals for extended interactions. These cannot be dismissed by clicking outside or pressing Escape."
                        TextWrapping="Wrap"
                        FontSize="12"
                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </controls:ExampleContainer.Controls>

            <StackPanel Spacing="16" Width="400">
                <husk:Card Padding="16">
                    <StackPanel Spacing="12">
                        <TextBlock Text="Modal Purpose" FontSize="16" FontWeight="Bold" />
                        <TextBlock
                            Text="Modals are designed for long, complex interactions that require the user's full attention. Unlike dialogs, they cannot be dismissed by clicking outside or pressing Escape."
                            TextWrapping="Wrap" />
                        <TextBlock
                            Text="Use modals for settings pages, user profiles, complex forms, or any interaction that requires completion of a full workflow."
                            TextWrapping="Wrap"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </husk:Card>
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Complex Workflow Modals"
                                   Description="Modals for multi-step processes and detailed configurations">
            <controls:ExampleContainer.Controls>
                <StackPanel Spacing="12">
                    <TextBlock Text="Complex Workflow Controls" FontWeight="Bold" FontSize="14" />
                    <Button Content="Project Setup Wizard" Click="OnShowProjectWizardClick" />
                    <TextBlock
                        Text="These modals contain complex workflows that require multiple steps or detailed configuration."
                        TextWrapping="Wrap"
                        FontSize="12"
                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </controls:ExampleContainer.Controls>

            <StackPanel Spacing="16" Width="450">
                <husk:Card Padding="16">
                    <StackPanel Spacing="12">
                        <TextBlock Text="Complex Interaction Features" FontSize="16" FontWeight="Bold" />
                        <StackPanel Spacing="8">
                            <TextBlock Text="• Multi-step wizards and workflows" />
                            <TextBlock Text="• Detailed form validation and processing" />
                            <TextBlock Text="• Tabbed interfaces and navigation" />
                            <TextBlock Text="• Complete feature configuration" />
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </StackPanel>
        </controls:ExampleContainer>

        <controls:ExampleContainer Title="Modal Best Practices"
                                   Description="Guidelines for effective modal usage in long interactions">
            <StackPanel Spacing="16" Width="600">
                <husk:Card Padding="20">
                    <StackPanel Spacing="16">
                        <TextBlock Text="Design Guidelines" FontSize="18" FontWeight="Bold" />

                        <StackPanel Spacing="12">
                            <StackPanel Spacing="4">
                                <TextBlock Text="✅ Do" FontWeight="Bold" Foreground="Green" />
                                <TextBlock Text="• Use for complex, multi-step interactions" />
                                <TextBlock Text="• Provide clear navigation and progress indicators" />
                                <TextBlock Text="• Include explicit close/save/cancel actions" />
                                <TextBlock Text="• Design for extended user engagement" />
                            </StackPanel>

                            <husk:Divider />

                            <StackPanel Spacing="4">
                                <TextBlock Text="❌ Don't" FontWeight="Bold" Foreground="Red" />
                                <TextBlock Text="• Use for simple confirmations (use Dialog instead)" />
                                <TextBlock Text="• Allow dismissal by clicking outside" />
                                <TextBlock Text="• Make them too small for the content" />
                                <TextBlock Text="• Use for quick, simple interactions" />
                            </StackPanel>
                        </StackPanel>

                        <husk:Divider />

                        <StackPanel Spacing="8">
                            <TextBlock Text="Technical Notes" FontWeight="Bold" />
                            <TextBlock
                                Text="Modals are designed for long, complex interactions that require the user's full attention. They cannot be dismissed by clicking outside or pressing Escape, ensuring users complete their intended workflow."
                                TextWrapping="Wrap"
                                FontSize="12"
                                Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </StackPanel>
        </controls:ExampleContainer>

    </StackPanel>

</controls:ControlPage>
