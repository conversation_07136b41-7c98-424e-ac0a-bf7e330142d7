# Product Overview

Huskui.Avalonia is a modern, elegant UI component library for Avalonia UI that provides a comprehensive set of customizable controls for building beautiful cross-platform desktop applications.

## Key Features
- Rich component library with 20+ UI controls (AppWindow, Card, InfoBar, Tag, IconLabel, Frame, etc.)
- Consistent design language inspired by ParkUI using Radix Colors palette
- Built-in light/dark theme support
- FluentIcons integration for consistent iconography
- Modern UI patterns: overlays, notifications, dialogs, toasts
- Production-ready and battle-tested

## Target Audience
Developers building cross-platform desktop applications with Avalonia UI who need a comprehensive, modern UI component library.

## Gallery Application
The project includes an interactive Gallery application that showcases all components with live examples and documentation. This serves as both a demo and comprehensive documentation for the library.